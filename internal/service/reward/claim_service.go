package reward

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/dto/response"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/nats"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/transaction"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"
)

// RewardClaimEvent represents a reward claim event
type RewardClaimEvent struct {
	ID        uuid.UUID       `json:"id"`
	UserID    uuid.UUID       `json:"user_id"`
	Address   string          `json:"address"`
	Amount    decimal.Decimal `json:"amount"`
	Token     string          `json:"token"`
	ChainID   int             `json:"chain_id"`
	CreatedAt string          `json:"created_at"`
}

// RewardEvent represents reward event types
type RewardEvent string

const (
	RewardSubmitClaimEventSubject RewardEvent = "reward.claim.execute"
)

// ClaimService handles reward claiming operations
type ClaimService struct {
	commissionRepo       transaction.CommissionLedgerRepositoryInterface
	activityCashbackRepo transaction.ActivityCashbackRepositoryInterface
	userRepo             transaction.UserRepositoryInterface
	natsPublisher        nats.Publisher
}

// NewClaimService creates a new claim service
func NewClaimService() service.ClaimI {
	return &ClaimService{
		commissionRepo:       transaction.NewCommissionLedgerRepository(),
		activityCashbackRepo: transaction.NewActivityCashbackRepository(),
		userRepo:             transaction.NewUserRepository(),
		natsPublisher:        global.GVA_NATS_DEX,
	}
}

func (s *ClaimService) GetClaimReward(ctx context.Context, userID uuid.UUID) (*response.ClaimRewardResponse, error) {
	var totalCashbackSOL decimal.Decimal
	var totalCommissionUSD decimal.Decimal
	pendingCashbacks, err := s.activityCashbackRepo.GetPendingCashbacksByUserID(ctx, userID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get pending cashback", zap.Error(err))
	} else {
		for _, cashback := range pendingCashbacks {
			totalCashbackSOL = totalCashbackSOL.Add(cashback.CashbackAmountSOL)
		}
	}

	pendingCommissions, err := s.commissionRepo.GetPendingCommissionsByUserID(ctx, userID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get pending rebate", zap.Error(err))
		global.GVA_LOG.Error("Failed to get pending commissions", zap.Error(err))
	} else {
		for _, commission := range pendingCommissions {
			totalCommissionUSD = totalCommissionUSD.Add(commission.CommissionAmount)
		}
	}

	return &response.ClaimRewardResponse{
		ClaimMeme:     totalCashbackSOL.String(),
		ClaimContract: totalCommissionUSD.String(),
	}, nil
}

// ClaimMemeReward claims all pending activity cashback rewards for a user
func (s *ClaimService) ClaimMemeReward(ctx context.Context, userID uuid.UUID, address string) error {
	global.GVA_LOG.Info("Starting activity cashback claim process", zap.String("user_id", userID.String()))

	// Get pending activity cashbacks
	pendingCashbacks, err := s.activityCashbackRepo.GetPendingCashbacksByUserID(ctx, userID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get pending activity cashbacks", zap.Error(err))
		return fmt.Errorf("failed to get pending activity cashbacks: %w", err)
	}

	// Calculate total amounts
	var totalCashbackUSD decimal.Decimal
	var totalCashbackSOL decimal.Decimal

	cashbackIDs := make([]uuid.UUID, 0, len(pendingCashbacks))
	for _, cashback := range pendingCashbacks {
		cashbackIDs = append(cashbackIDs, cashback.ID)
		totalCashbackSOL = totalCashbackSOL.Add(cashback.CashbackAmountSOL)
		totalCashbackUSD = totalCashbackUSD.Add(cashback.CashbackAmountUSD)
	}

	// Validate that we have something to claim
	if totalCashbackSOL.IsZero() {
		return fmt.Errorf("no pending rewards to claim")
	}

	// Start database transaction
	tx := global.GVA_DB.WithContext(ctx).Begin()
	if tx.Error != nil {
		return fmt.Errorf("failed to start transaction: %w", tx.Error)
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Update cashback status to CLAIMED
	if len(cashbackIDs) > 0 {
		now := time.Now()
		err = tx.Model(&model.ActivityCashback{}).
			Where("id IN ? AND status = ?", cashbackIDs, "PENDING_CLAIM").
			Updates(map[string]interface{}{
				"status":     "CLAIMED",
				"claimed_at": &now,
				"updated_at": &now,
			}).Error

		if err != nil {
			tx.Rollback()
			global.GVA_LOG.Error("Failed to update cashback status", zap.Error(err))
			return fmt.Errorf("failed to update cashback status: %w", err)
		}
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		global.GVA_LOG.Error("Failed to commit transaction", zap.Error(err))
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Get user wallet address
	// walletAddress, err := s.userRepo.GetUserWalletAddress(ctx, userID)
	// if err != nil {
	// 	global.GVA_LOG.Error("Failed to get user wallet address", zap.Error(err))
	// 	return fmt.Errorf("failed to get user wallet address: %w", err)
	// }

	// todo : validate address belongs to our system
	// Send reward claim event to NATS for each cashback
	event := RewardClaimEvent{
		ID:        cashbackIDs[0],
		UserID:    userID,
		Address:   address,
		Amount:    totalCashbackSOL,
		Token:     utils.WSOL_ADDRESS,
		ChainID:   int(utils.ChainIDSolanaInt),
		CreatedAt: time.Now().UTC().Format(time.RFC3339),
	}

	eventData, err := json.Marshal(event)
	if err != nil {
		global.GVA_LOG.Error("Failed to marshal reward claim event", zap.Error(err))
		return err
	}

	// Publish to NATS
	if _, err := s.natsPublisher.PublishJS(string(RewardSubmitClaimEventSubject), eventData); err != nil {
		global.GVA_LOG.Error("Failed to publish reward claim event to NATS",
			zap.Error(err),
			zap.String("user_id", userID.String()),
		)
		return err
	}

	global.GVA_LOG.Info("Published reward claim event to NATS",
		zap.String("user_id", userID.String()),
		zap.String("amount", totalCashbackSOL.String()),
		zap.String("address", address))

	global.GVA_LOG.Info("Transfer details for activity cashback claim",
		zap.String("user_id", userID.String()),
		zap.String("cashback_usd", totalCashbackUSD.String()),
		zap.String("cashback_sol", totalCashbackSOL.String()),
	)

	return nil
}

// ClaimContractReward claims all pending CONTRACT rewards for a user
func (s *ClaimService) ClaimContractReward(ctx context.Context, userID uuid.UUID) error {
	global.GVA_LOG.Info("Starting CONTRACT reward claim process", zap.String("user_id", userID.String()))

	// Get pending CONTRACT commissions
	pendingCommissions, err := s.commissionRepo.GetPendingCommissionsByUserID(ctx, userID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get pending CONTRACT commissions", zap.Error(err))
		return fmt.Errorf("failed to get pending CONTRACT commissions: %w", err)
	}

	if len(pendingCommissions) == 0 {
		return fmt.Errorf("no pending CONTRACT commissions found for user")
	}

	// Calculate total amounts
	var totalCommissionUSD decimal.Decimal

	commissionIDs := make([]uuid.UUID, 0, len(pendingCommissions))
	for _, commission := range pendingCommissions {
		commissionIDs = append(commissionIDs, commission.ID)
		totalCommissionUSD = totalCommissionUSD.Add(commission.CommissionAmount)
	}

	// Validate that we have something to claim
	if totalCommissionUSD.IsZero() {
		return fmt.Errorf("no pending CONTRACT rewards to claim")
	}

	// Start database transaction
	tx := global.GVA_DB.WithContext(ctx).Begin()
	if tx.Error != nil {
		return fmt.Errorf("failed to start transaction: %w", tx.Error)
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Update commission status to CLAIMED
	now := time.Now()
	err = tx.Model(&model.CommissionLedger{}).
		Where("id IN ? AND status = ?", commissionIDs, "PENDING_CLAIM").
		Updates(map[string]interface{}{
			"status":     "CLAIMED",
			"claimed_at": &now,
			"updated_at": &now,
		}).Error

	if err != nil {
		tx.Rollback()
		global.GVA_LOG.Error("Failed to update commission status", zap.Error(err))
		return fmt.Errorf("failed to update commission status: %w", err)
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		global.GVA_LOG.Error("Failed to commit transaction", zap.Error(err))
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Get user wallet address
	walletAddress, err := s.userRepo.GetUserWalletAddress(ctx, userID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get user wallet address", zap.Error(err))
		return fmt.Errorf("failed to get user wallet address: %w", err)
	}

	// Send reward claim event to NATS for each commission
	for _, commission := range pendingCommissions {
		event := RewardClaimEvent{
			ID:        commission.ID,
			UserID:    userID,
			Address:   walletAddress,
			Amount:    commission.CommissionAmount,
			Token:     "******************************************", // USDC contract address on Arbitrum
			ChainID:   42161,                                        // Arbitrum chain ID
			CreatedAt: time.Now().UTC().Format(time.RFC3339),
		}

		eventData, err := json.Marshal(event)
		if err != nil {
			global.GVA_LOG.Error("Failed to marshal reward claim event", zap.Error(err))
			continue
		}

		// Publish to NATS
		if _, err := s.natsPublisher.PublishJS(string(RewardSubmitClaimEventSubject), eventData); err != nil {
			global.GVA_LOG.Error("Failed to publish reward claim event to NATS",
				zap.Error(err),
				zap.String("user_id", userID.String()),
				zap.String("commission_id", commission.ID.String()))
			continue
		}

		global.GVA_LOG.Info("Published reward claim event to NATS",
			zap.String("user_id", userID.String()),
			zap.String("commission_id", commission.ID.String()),
			zap.String("amount", commission.CommissionAmount.String()),
			zap.String("address", walletAddress))
	}

	global.GVA_LOG.Info("Transfer details for CONTRACT reward claim",
		zap.String("user_id", userID.String()),
		zap.String("commission_usd", totalCommissionUSD.String()),
	)

	return nil
}
